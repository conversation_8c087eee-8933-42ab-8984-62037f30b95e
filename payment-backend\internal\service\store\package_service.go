package store

import (
	"fmt"
	"time"

	"github.com/google/uuid"

	"payment-backend/internal/domain/store"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"
)

// packageService 流量包服务实现
type packageService struct {
	packageRepo store.PackageRepository
	logger      logger.Logger
}

// NewPackageService 创建流量包服务
func NewPackageService(packageRepo store.PackageRepository, logger logger.Logger) store.PackageService {
	return &packageService{
		packageRepo: packageRepo,
		logger:      logger,
	}
}

// ListAllPackages 获取所有流量包（终端用户）
func (s *packageService) ListAllPackages(userCtx *middleware.UserContext, filter *store.PackageFilter, pagination *store.PaginationRequest) (*store.ListPackagesResponse, error) {
	// 如果没有传入过滤条件，使用默认值
	if filter == nil {
		filter = &store.PackageFilter{}
	}

	// 如果没有传入国家和货币，使用默认值
	if filter.Country == nil && filter.Currency == nil {
		defaultCountry := "US"
		defaultCurrency := "USD"
		filter.Country = &defaultCountry
		filter.Currency = &defaultCurrency
	}

	packages, total, err := s.packageRepo.List(filter, pagination)
	if err != nil {
		return nil, fmt.Errorf("failed to list packages: %w", err)
	}

	// 转换为用户响应格式
	userPackages := make([]*store.PackageResponse, 0, len(packages))
	for _, pkg := range packages {
		userPackages = append(userPackages, pkg.ToUserResponse())
	}

	// 计算分页信息
	remaining := total - int64(pagination.Offset+len(userPackages))
	if remaining < 0 {
		remaining = 0
	}

	return &store.ListPackagesResponse{
		Packages: userPackages,
		Pagination: &store.PaginationResponse{
			Total:     total,
			Limit:     pagination.Limit,
			Offset:    pagination.Offset,
			Remaining: remaining,
		},
	}, nil
}

// AdminAddPackages 添加流量包（管理员）
func (s *packageService) AdminAddPackages(req *store.CreatePackageRequest) error {
	// 创建新的流量包
	pkg := &store.Package{
		PackageID:         uuid.New().String(),
		PackageName:       req.PackageName,
		PackageDesc:       req.PackageDesc,
		Entitlement:       req.Entitlement,
		OriginalPrice:     req.OriginalPrice,
		DiscountPrice:     req.DiscountPrice,
		DiscountStartTime: req.DiscountStartTime,
		DiscountEndTime:   req.DiscountEndTime,
		SaleStatus:        req.SaleStatus,
		Currency:          req.Currency,
		Country:           req.Country,
		Extra1:            req.Extra1,
		Extra2:            req.Extra2,
		Extra3:            req.Extra3,
		Extra4:            req.Extra4,
		Deleted:           false,
	}

	// 设置默认值
	if pkg.Currency == "" {
		pkg.Currency = "USD"
	}
	if pkg.Country == "" {
		pkg.Country = "US"
	}
	if pkg.SaleStatus == "" {
		pkg.SaleStatus = "on_sale"
	}

	// 验证价格逻辑
	if pkg.DiscountPrice != nil && *pkg.DiscountPrice >= pkg.OriginalPrice {
		return fmt.Errorf("discount price must be less than original price")
	}

	// 验证优惠时间逻辑
	if pkg.DiscountStartTime != nil && pkg.DiscountEndTime != nil {
		if pkg.DiscountEndTime.Before(*pkg.DiscountStartTime) {
			return fmt.Errorf("discount end time must be after start time")
		}
	}

	// 验证出售状态
	if pkg.SaleStatus != "on_sale" && pkg.SaleStatus != "off_sale" {
		return fmt.Errorf("sale status must be 'on_sale' or 'off_sale'")
	}

	// 保存到数据库
	if err := s.packageRepo.Create(pkg); err != nil {
		return fmt.Errorf("failed to create package: %w", err)
	}

	s.logger.Info("Package created successfully", logger.String("package_id", pkg.PackageID))
	return nil
}

// AdminDeletePackages 删除流量包（管理员）
func (s *packageService) AdminDeletePackages(req *store.DeletePackageRequest) error {
	// 设置默认值
	if req.Currency == "" {
		req.Currency = "USD"
	}
	if req.Country == "" {
		req.Country = "US"
	}

	// 检查流量包是否存在
	pkg, err := s.packageRepo.GetByPackageID(req.PackageID)
	if err != nil {
		return fmt.Errorf("package not found: %w", err)
	}

	// 验证国家或货币匹配
	if req.Currency != "" && pkg.Currency != req.Currency {
		return fmt.Errorf("package currency does not match")
	}
	if req.Country != "" && pkg.Country != req.Country {
		return fmt.Errorf("package country does not match")
	}

	// 软删除
	if err := s.packageRepo.SoftDelete(req.PackageID); err != nil {
		return fmt.Errorf("failed to delete package: %w", err)
	}

	s.logger.Info("Package deleted successfully", logger.String("package_id", req.PackageID))
	return nil
}

// AdminUpdatePackages 更新流量包（管理员）
func (s *packageService) AdminUpdatePackages(req *store.UpdatePackageRequest) error {
	// 获取现有流量包
	pkg, err := s.packageRepo.GetByPackageID(req.PackageID)
	if err != nil {
		return fmt.Errorf("package not found: %w", err)
	}

	// 更新字段
	if req.PackageName != nil {
		pkg.PackageName = *req.PackageName
	}
	if req.PackageDesc != nil {
		pkg.PackageDesc = *req.PackageDesc
	}
	if req.Entitlement != nil {
		pkg.Entitlement = *req.Entitlement
	}
	if req.OriginalPrice != nil {
		pkg.OriginalPrice = *req.OriginalPrice
	}
	if req.DiscountPrice != nil {
		pkg.DiscountPrice = req.DiscountPrice
	}
	if req.DiscountStartTime != nil {
		pkg.DiscountStartTime = req.DiscountStartTime
	}
	if req.DiscountEndTime != nil {
		pkg.DiscountEndTime = req.DiscountEndTime
	}
	if req.SaleStatus != nil {
		pkg.SaleStatus = *req.SaleStatus
	}
	if req.Currency != nil {
		pkg.Currency = *req.Currency
	}
	if req.Country != nil {
		pkg.Country = *req.Country
	}
	if req.Extra1 != nil {
		pkg.Extra1 = *req.Extra1
	}
	if req.Extra2 != nil {
		pkg.Extra2 = *req.Extra2
	}
	if req.Extra3 != nil {
		pkg.Extra3 = *req.Extra3
	}
	if req.Extra4 != nil {
		pkg.Extra4 = *req.Extra4
	}

	// 验证价格逻辑
	if pkg.DiscountPrice != nil && *pkg.DiscountPrice >= pkg.OriginalPrice {
		return fmt.Errorf("discount price must be less than original price")
	}

	// 验证优惠时间逻辑
	if pkg.DiscountStartTime != nil && pkg.DiscountEndTime != nil {
		if pkg.DiscountEndTime.Before(*pkg.DiscountStartTime) {
			return fmt.Errorf("discount end time must be after start time")
		}
	}

	// 验证出售状态
	if pkg.SaleStatus != "on_sale" && pkg.SaleStatus != "off_sale" {
		return fmt.Errorf("sale status must be 'on_sale' or 'off_sale'")
	}

	// 更新时间
	now := time.Now()
	pkg.UpdatedAt = &now

	// 保存到数据库
	if err := s.packageRepo.Update(pkg); err != nil {
		return fmt.Errorf("failed to update package: %w", err)
	}

	s.logger.Info("Package updated successfully", logger.String("package_id", req.PackageID))
	return nil
}

// AdminListAllPackages 获取所有流量包（管理员）
func (s *packageService) AdminListAllPackages(filter *store.PackageFilter, pagination *store.PaginationRequest) (*store.AdminListPackagesResponse, error) {
	// 如果没有传入过滤条件，使用默认值
	if filter == nil {
		filter = &store.PackageFilter{}
	}

	// 如果没有传入国家和货币，使用默认值
	if filter.Country == nil && filter.Currency == nil {
		defaultCountry := "US"
		defaultCurrency := "USD"
		filter.Country = &defaultCountry
		filter.Currency = &defaultCurrency
	}

	packages, total, err := s.packageRepo.List(filter, pagination)
	if err != nil {
		return nil, fmt.Errorf("failed to list packages: %w", err)
	}

	// 转换为管理员响应格式
	adminPackages := make([]*store.AdminPackageResponse, 0, len(packages))
	for _, pkg := range packages {
		adminPackages = append(adminPackages, pkg.ToAdminResponse())
	}

	// 计算分页信息
	remaining := total - int64(pagination.Offset+len(adminPackages))
	if remaining < 0 {
		remaining = 0
	}

	return &store.AdminListPackagesResponse{
		Packages: adminPackages,
		Pagination: &store.PaginationResponse{
			Total:     total,
			Limit:     pagination.Limit,
			Offset:    pagination.Offset,
			Remaining: remaining,
		},
	}, nil
}
