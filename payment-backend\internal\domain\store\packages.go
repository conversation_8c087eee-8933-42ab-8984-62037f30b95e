package store

import (
	"payment-backend/internal/middleware"
	"time"
)

// Package 流量包实体
type Package struct {
	ID                uint64     `json:"id"`
	PackageID         string     `json:"package_id"`          // 流量包ID varchar(128), 使用 uuid v4
	PackageName       string     `json:"package_name"`        // 流量包名称 varchar(128)
	PackageDesc       string     `json:"package_desc"`        // 流量包描述 varchar(1024)
	Entitlement       int32      `json:"entitlement"`         // 购买后获得的权益 int32
	OriginalPrice     float64    `json:"original_price"`      // 流量包原价
	DiscountPrice     *float64   `json:"discount_price"`      // 流量包优惠价，可为空
	DiscountStartTime *time.Time `json:"discount_start_time"` // 优惠开始时间
	DiscountEndTime   *time.Time `json:"discount_end_time"`   // 优惠结束时间
	SaleStatus        string     `json:"sale_status"`         // 出售状态：on_sale(起售), off_sale(停售)
	Currency          string     `json:"currency"`            // 货币单位 char(3)
	Country           string     `json:"country"`             // 国家
	Extra1            string     `json:"extra1"`              // 附件字段1 varchar(512)
	Extra2            string     `json:"extra2"`              // 附件字段2 varchar(512)
	Extra3            int32      `json:"extra3"`              // 附件字段3 int32
	Extra4            int32      `json:"extra4"`              // 附件字段4 int32
	CreatedAt         *time.Time `json:"created_at"`
	UpdatedAt         *time.Time `json:"updated_at"`
	Deleted           bool       `json:"deleted"`
	DeletedAt         *time.Time `json:"deleted_at,omitempty"`
}

// PackageResponse 流量包响应（终端用户）
type PackageResponse struct {
	PackageID         string     `json:"package_id"`
	PackageName       string     `json:"package_name"`
	PackageDesc       string     `json:"package_desc"`
	Entitlement       int32      `json:"entitlement"`
	Price             float64    `json:"price"`               // 实际价格（优惠价或原价）
	OriginalPrice     *float64   `json:"original_price"`      // 原价（仅在有优惠时显示）
	DiscountStartTime *time.Time `json:"discount_start_time"` // 优惠开始时间
	DiscountEndTime   *time.Time `json:"discount_end_time"`   // 优惠结束时间
	SaleStatus        string     `json:"sale_status"`         // 出售状态
	Currency          string     `json:"currency"`
	Country           string     `json:"country"`
}

// AdminPackageResponse 流量包响应（管理员）
type AdminPackageResponse struct {
	ID                uint64     `json:"id"`
	PackageID         string     `json:"package_id"`
	PackageName       string     `json:"package_name"`
	PackageDesc       string     `json:"package_desc"`
	Entitlement       int32      `json:"entitlement"`
	OriginalPrice     float64    `json:"original_price"`
	DiscountPrice     *float64   `json:"discount_price"`
	DiscountStartTime *time.Time `json:"discount_start_time"`
	DiscountEndTime   *time.Time `json:"discount_end_time"`
	SaleStatus        string     `json:"sale_status"`
	Currency          string     `json:"currency"`
	Country           string     `json:"country"`
	Extra1            string     `json:"extra1"`
	Extra2            string     `json:"extra2"`
	Extra3            int32      `json:"extra3"`
	Extra4            int32      `json:"extra4"`
	CreatedAt         *time.Time `json:"created_at"`
	UpdatedAt         *time.Time `json:"updated_at"`
}

// CreatePackageRequest 创建流量包请求
type CreatePackageRequest struct {
	PackageName       string     `json:"package_name" binding:"required" validate:"required" comment:"流量包名称"`
	PackageDesc       string     `json:"package_desc" binding:"required" validate:"required" comment:"流量包描述"`
	Entitlement       int32      `json:"entitlement" binding:"required" validate:"required" comment:"购买后获得的权益"`
	OriginalPrice     float64    `json:"original_price" binding:"required,gt=0" validate:"required,gt=0" comment:"流量包原价"`
	DiscountPrice     *float64   `json:"discount_price" validate:"omitempty,gt=0" comment:"流量包优惠价"`
	DiscountStartTime *time.Time `json:"discount_start_time" comment:"优惠开始时间"`
	DiscountEndTime   *time.Time `json:"discount_end_time" comment:"优惠结束时间"`
	SaleStatus        string     `json:"sale_status" comment:"出售状态：on_sale(起售), off_sale(停售)，默认on_sale"`
	Currency          string     `json:"currency" comment:"货币单位，默认USD"`
	Country           string     `json:"country" comment:"国家，默认US"`
	Extra1            string     `json:"extra1" comment:"附件字段1"`
	Extra2            string     `json:"extra2" comment:"附件字段2"`
	Extra3            int32      `json:"extra3" comment:"附件字段3"`
	Extra4            int32      `json:"extra4" comment:"附件字段4"`
}

// UpdatePackageRequest 更新流量包请求
type UpdatePackageRequest struct {
	PackageID         string     `json:"package_id" binding:"required" validate:"required" comment:"流量包ID"`
	PackageName       *string    `json:"package_name" comment:"流量包名称"`
	PackageDesc       *string    `json:"package_desc" comment:"流量包描述"`
	Entitlement       *int32     `json:"entitlement" comment:"购买后获得的权益"`
	OriginalPrice     *float64   `json:"original_price" validate:"omitempty,gt=0" comment:"流量包原价"`
	DiscountPrice     *float64   `json:"discount_price" validate:"omitempty,gt=0" comment:"流量包优惠价"`
	DiscountStartTime *time.Time `json:"discount_start_time" comment:"优惠开始时间"`
	DiscountEndTime   *time.Time `json:"discount_end_time" comment:"优惠结束时间"`
	SaleStatus        *string    `json:"sale_status" comment:"出售状态"`
	Currency          *string    `json:"currency" comment:"货币单位"`
	Country           *string    `json:"country" comment:"国家"`
	Extra1            *string    `json:"extra1" comment:"附件字段1"`
	Extra2            *string    `json:"extra2" comment:"附件字段2"`
	Extra3            *int32     `json:"extra3" comment:"附件字段3"`
	Extra4            *int32     `json:"extra4" comment:"附件字段4"`
}

// DeletePackageRequest 删除流量包请求
type DeletePackageRequest struct {
	PackageID string `json:"package_id" binding:"required" validate:"required" comment:"流量包ID"`
	Currency  string `json:"currency" comment:"货币单位，默认USD"`
	Country   string `json:"country" comment:"国家，默认US"`
}

// PackageFilter 流量包过滤条件
type PackageFilter struct {
	Currency *string `json:"currency,omitempty" form:"currency" comment:"货币单位"`
	Country  *string `json:"country,omitempty" form:"country" comment:"国家"`
}

// PaginationRequest 分页请求
type PaginationRequest struct {
	Limit  int `json:"limit" form:"limit" binding:"min=1,max=500" comment:"每页数量，最小1，最大500"`
	Offset int `json:"offset" form:"offset" binding:"min=0" comment:"偏移量，从0开始"`
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Total     int64 `json:"total" comment:"总记录数"`
	Limit     int   `json:"limit" comment:"每页数量"`
	Offset    int   `json:"offset" comment:"偏移量"`
	Remaining int64 `json:"remaining" comment:"剩余记录数"`
}

// ListPackagesResponse 流量包列表响应（终端用户）
type ListPackagesResponse struct {
	Packages   []*PackageResponse  `json:"packages" comment:"流量包列表"`
	Pagination *PaginationResponse `json:"pagination" comment:"分页信息"`
}

// AdminListPackagesResponse 流量包列表响应（管理员）
type AdminListPackagesResponse struct {
	Packages   []*AdminPackageResponse `json:"packages" comment:"流量包列表"`
	Pagination *PaginationResponse     `json:"pagination" comment:"分页信息"`
}

// GetEffectivePrice 获取有效价格
func (p *Package) GetEffectivePrice() float64 {
	// 检查优惠价是否有效（有优惠价且在有效期内）
	if p.DiscountPrice != nil && *p.DiscountPrice > 0 && p.IsDiscountValid() {
		return *p.DiscountPrice
	}
	// 使用原价
	return p.OriginalPrice
}

// IsDiscountValid 检查优惠是否有效
func (p *Package) IsDiscountValid() bool {
	now := time.Now()

	// 如果没有设置优惠时间，则认为优惠永久有效
	if p.DiscountStartTime == nil && p.DiscountEndTime == nil {
		return true
	}

	// 检查是否在优惠时间范围内
	if p.DiscountStartTime != nil && now.Before(*p.DiscountStartTime) {
		return false // 优惠还未开始
	}

	if p.DiscountEndTime != nil && now.After(*p.DiscountEndTime) {
		return false // 优惠已结束
	}

	return true
}

// ToUserResponse 转换为终端用户响应
func (p *Package) ToUserResponse() *PackageResponse {
	effectivePrice := p.GetEffectivePrice()
	response := &PackageResponse{
		PackageID:         p.PackageID,
		PackageName:       p.PackageName,
		PackageDesc:       p.PackageDesc,
		Entitlement:       p.Entitlement,
		Price:             effectivePrice,
		DiscountStartTime: p.DiscountStartTime,
		DiscountEndTime:   p.DiscountEndTime,
		SaleStatus:        p.SaleStatus,
		Currency:          p.Currency,
		Country:           p.Country,
	}

	// 只有在有优惠时才显示原价
	if effectivePrice < p.OriginalPrice {
		response.OriginalPrice = &p.OriginalPrice
	}

	return response
}

// ToAdminResponse 转换为管理员响应
func (p *Package) ToAdminResponse() *AdminPackageResponse {
	return &AdminPackageResponse{
		ID:                p.ID,
		PackageID:         p.PackageID,
		PackageName:       p.PackageName,
		PackageDesc:       p.PackageDesc,
		Entitlement:       p.Entitlement,
		OriginalPrice:     p.OriginalPrice,
		DiscountPrice:     p.DiscountPrice,
		DiscountStartTime: p.DiscountStartTime,
		DiscountEndTime:   p.DiscountEndTime,
		SaleStatus:        p.SaleStatus,
		Currency:          p.Currency,
		Country:           p.Country,
		Extra1:            p.Extra1,
		Extra2:            p.Extra2,
		Extra3:            p.Extra3,
		Extra4:            p.Extra4,
		CreatedAt:         p.CreatedAt,
		UpdatedAt:         p.UpdatedAt,
	}
}

// SoftDelete 软删除流量包
func (p *Package) SoftDelete() {
	now := time.Now()
	p.Deleted = true
	p.DeletedAt = &now
	p.UpdatedAt = &now
}

// PackageRepository 流量包仓储接口
type PackageRepository interface {
	Create(pkg *Package) error
	GetByID(id uint64) (*Package, error)
	GetByPackageID(packageID string) (*Package, error)
	Update(pkg *Package) error
	SoftDelete(packageID string) error
	List(filter *PackageFilter, pagination *PaginationRequest) ([]*Package, int64, error)
	ListByCountryOrCurrency(country, currency string, pagination *PaginationRequest) ([]*Package, int64, error)
}

// PackageService 流量包服务接口
type PackageService interface {
	// 终端用户接口
	ListAllPackages(userCtx *middleware.UserContext, filter *PackageFilter, pagination *PaginationRequest) (*ListPackagesResponse, error)

	// 管理员接口
	AdminAddPackages(req *CreatePackageRequest) error
	AdminDeletePackages(req *DeletePackageRequest) error
	AdminUpdatePackages(req *UpdatePackageRequest) error
	AdminListAllPackages(filter *PackageFilter, pagination *PaginationRequest) (*AdminListPackagesResponse, error)
}
